import { useState, useRef, useCallback } from 'react';
import { AudioPlayerState } from '../../types/audio';

export function useAudioPlayer() {
  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    isGenerating: false,
    isTranscribing: false,
    hasAudio: false,
    audioUrl: null,
    error: null,
    currentTime: 0,
    currentWordIndex: -1,
  });

  const audioRef = useRef<HTMLAudioElement>(null);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const setIsPlaying = useCallback((isPlaying: boolean) => {
    setState(prev => ({ ...prev, isPlaying }));
  }, []);

  const setIsGenerating = useCallback((isGenerating: boolean) => {
    setState(prev => ({ ...prev, isGenerating }));
  }, []);

  const setHasAudio = useCallback((hasAudio: boolean) => {
    setState(prev => ({ ...prev, hasAudio }));
  }, []);

  const setAudioUrl = useCallback((audioUrl: string | null) => {
    setState(prev => ({ ...prev, audioUrl }));
  }, []);

  const setCurrentTime = useCallback((currentTime: number) => {
    setState(prev => ({ ...prev, currentTime }));
  }, []);

  const setCurrentWordIndex = useCallback((currentWordIndex: number) => {
    setState(prev => ({ ...prev, currentWordIndex }));
  }, []);

  const loadExistingAudio = useCallback(async (storyId: string, autoPlay = false) => {
    setError(null);

    try {
      const response = await fetch('/api/generate-audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          story_id: storyId,
          force_regenerate: false 
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          setError(data.message || 'Premium subscription required for audio generation');
        } else {
          setError(data.error || 'Failed to generate audio');
        }
        return;
      }

      if (!data.audio_url) {
        throw new Error('No audio URL in response');
      }

      const newAudioUrl = data.audio_url;
      setAudioUrl(newAudioUrl);
      
      if (autoPlay) {
        setTimeout(async () => {
          try {
            if (audioRef.current) {
              audioRef.current.load();
              
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  reject(new Error('Audio load timeout'));
                }, 10000);
                
                const onCanPlay = () => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  resolve(undefined);
                };
                
                const onError = (e: Event) => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  reject(e);
                };
                
                audioRef.current?.addEventListener('canplay', onCanPlay);
                audioRef.current?.addEventListener('error', onError);
              });
              
              await audioRef.current.play();
              setIsPlaying(true);
            }
          } catch (playError) {
            setError(`Failed to play audio: ${playError instanceof Error ? playError.message : 'Unknown error'}`);
          }
        }, 500);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load audio');
    }
  }, [setError, setAudioUrl, setIsPlaying]);

  const generateAudio = useCallback(async (storyId: string, forceRegenerate = false, autoPlay = false) => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          story_id: storyId,
          force_regenerate: forceRegenerate 
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          setError(data.message || 'Premium subscription required for audio generation');
        } else {
          setError(data.error || 'Failed to generate audio');
        }
        return;
      }

      if (!data.audio_url) {
        throw new Error('No audio URL in response');
      }

      const newAudioUrl = data.audio_url;
      setAudioUrl(newAudioUrl);
      setHasAudio(true);
      
      if (autoPlay) {
        setTimeout(async () => {
          try {
            if (audioRef.current) {
              audioRef.current.load();
              
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  reject(new Error('Audio load timeout'));
                }, 10000);
                
                const onCanPlay = () => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  resolve(undefined);
                };
                
                const onError = (e: Event) => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  reject(e);
                };
                
                audioRef.current?.addEventListener('canplay', onCanPlay);
                audioRef.current?.addEventListener('error', onError);
              });
              
              await audioRef.current.play();
              setIsPlaying(true);
            }
          } catch (playError) {
            setError(`Failed to play audio: ${playError instanceof Error ? playError.message : 'Unknown error'}`);
          }
        }, 500);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate audio');
    } finally {
      setIsGenerating(false);
    }
  }, [setIsGenerating, setError, setAudioUrl, setHasAudio, setIsPlaying]);

  const togglePlayback = useCallback(async () => {
    if (!audioRef.current || !state.audioUrl) {
      return;
    }

    if (state.isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      try {
        if (audioRef.current.error || isNaN(audioRef.current.duration)) {
          audioRef.current.load();
          
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Audio load timeout')), 10000);
            
            const onCanPlay = () => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              resolve(undefined);
            };
            
            const onError = (e: Event) => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              reject(e);
            };
            
            audioRef.current?.addEventListener('canplay', onCanPlay);
            audioRef.current?.addEventListener('error', onError);
          });
        }
        
        await audioRef.current.play();
        setIsPlaying(true);
        setError(null);
      } catch (err) {
        setError(`Failed to play audio: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsPlaying(false);
      }
    }
  }, [state.audioUrl, state.isPlaying, setIsPlaying, setError]);

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, [setCurrentTime]);

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false);
    setCurrentWordIndex(-1);
  }, [setIsPlaying, setCurrentWordIndex]);

  const handleAudioError = useCallback(() => {
    setError(`Failed to load audio file: ${audioRef.current?.error?.message || 'Unknown error'}`);
    setIsPlaying(false);
  }, [setError, setIsPlaying]);

  const handleAudioLoaded = useCallback(() => {
    setError(null);
  }, [setError]);

  const seekToTime = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  }, []);

  return {
    state,
    audioRef,
    loadExistingAudio,
    generateAudio,
    togglePlayback,
    handleTimeUpdate,
    handleAudioEnded,
    handleAudioError,
    handleAudioLoaded,
    seekToTime,
    setCurrentWordIndex,
    setHasAudio,
    setIsPlaying,
    setAudioUrl,
  };
}