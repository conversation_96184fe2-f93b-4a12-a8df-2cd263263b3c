'use client';

import { useState, useRef, useCallback, useMemo, memo } from 'react';
import GeneratingDialog from './GeneratingDialog';
import { usePremiumFeatures } from '../hooks/usePremiumFeatures';
import Link from 'next/link';
import { audioApi, handleApiError, isUpgradeRequiredError } from '@/lib/utils/api';
import { useApiCall } from '@/app/hooks/api/useApiCall';

interface AudioPlayerProps {
  storyId: string;
  hasExistingAudio?: boolean;
}

const AudioPlayer = memo(function AudioPlayer({ storyId, hasExistingAudio = false }: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [hasAudio, setHasAudio] = useState(hasExistingAudio);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { premiumAccess, isLoading } = usePremiumFeatures();
  
  const audioCall = useApiCall<{ audio_url: string; message?: string }>();

  const generateAudio = useCallback(async (forceRegenerate = false, autoPlay = false) => {
    setError(null);

    try {
      const data = await audioCall.execute(() => 
        audioApi.generateAudio(storyId, forceRegenerate)
      );

      if (!data.audio_url) {
        throw new Error('No audio URL in response');
      }

      const newAudioUrl = data.audio_url;
      setAudioUrl(newAudioUrl);
      setHasAudio(true);
      
      // If autoPlay is true, start playing once the audio is loaded
      if (autoPlay) {
        // Use a longer timeout to ensure the audio element has updated
        setTimeout(async () => {
          try {
            if (audioRef.current) {
              audioRef.current.load();
              
              // Wait for the audio to be ready
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  reject(new Error('Audio load timeout'));
                }, 10000);
                
                const onCanPlay = () => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  resolve(undefined);
                };
                
                const onError = (e: Event) => {
                  clearTimeout(timeout);
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  reject(e);
                };
                
                audioRef.current?.addEventListener('canplay', onCanPlay);
                audioRef.current?.addEventListener('error', onError);
              });
              
              await audioRef.current.play();
              setIsPlaying(true);
            }
          } catch (playError) {
            setError(`Failed to play audio: ${playError instanceof Error ? playError.message : 'Unknown error'}`);
          }
        }, 500);
      }
      
    } catch (err) {
      const errorMessage = handleApiError(err);
      if (isUpgradeRequiredError(err)) {
        setError('Premium subscription required for audio generation');
      } else {
        setError(errorMessage);
      }
    }
  }, [storyId, audioCall]);

  const togglePlayback = useCallback(async () => {
    if (!audioRef.current || !audioUrl) {
      return;
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      try {
        if (audioRef.current.error || isNaN(audioRef.current.duration)) {
          audioRef.current.load();
          
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Audio load timeout')), 10000);
            
            const onCanPlay = () => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              resolve(undefined);
            };
            
            const onError = (e: Event) => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              reject(e);
            };
            
            audioRef.current?.addEventListener('canplay', onCanPlay);
            audioRef.current?.addEventListener('error', onError);
          });
        }
        
        await audioRef.current.play();
        setIsPlaying(true);
        setError(null);
      } catch (err) {
        setError(`Failed to play audio: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsPlaying(false);
      }
    }
  }, [audioUrl, isPlaying]);

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const handleAudioError = useCallback(() => {
    setError(`Failed to load audio file: ${audioRef.current?.error?.message || 'Unknown error'}`);
    setIsPlaying(false);
  }, []);

  const handleAudioLoaded = useCallback(() => {
    setError(null);
  }, []);

  const premiumFeatureContent = useMemo(() => (
    <div className="bg-slate-700 border border-slate-600 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-2">
        <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span className="text-sm font-medium text-white">Premium Feature</span>
      </div>
      <p className="text-sm text-gray-300 mb-2">
        Audio generation is only available for Starter and Family plan subscribers.
      </p>
      <p className="text-xs text-gray-400 mb-3">
        Current plan: <span className="font-medium">{premiumAccess?.planName || 'Free'}</span>
      </p>
      <Link 
        href="/pricing" 
        className="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
      >
        Upgrade Plan
      </Link>
    </div>
  ), [premiumAccess?.planName]);

  return (
    <div className="flex flex-col gap-2">
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onEnded={handleAudioEnded}
          onError={handleAudioError}
          onLoadedData={handleAudioLoaded}
          preload="metadata"
          controls={false}
        />
      )}
      
      {error && (
        <div className="text-red-400 text-sm mb-2">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="animate-pulse bg-slate-700 rounded-lg h-10 w-32"></div>
      ) : !premiumAccess?.hasAccess ? (
        premiumFeatureContent
      ) : (
        <div className="flex flex-col sm:flex-row gap-2">
          {!hasAudio ? (
            <button
              onClick={() => generateAudio()}
              disabled={audioCall.loading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
            >
              Listen to Story
            </button>
          ) : !audioUrl ? (
            <button
              onClick={() => generateAudio(false, true)} // autoPlay = true when clicking "Play Story"
              disabled={audioCall.loading}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <div className="w-0 h-0 border-l-4 border-l-white border-y-2 border-y-transparent"></div>
              Play Story
            </button>
          ) : (
            <button
              onClick={togglePlayback}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <div className="w-4 h-4 bg-white rounded-sm"></div>
                  Pause
                </>
              ) : (
                <>
                  <div className="w-0 h-0 border-l-4 border-l-white border-y-2 border-y-transparent"></div>
                  Play Story
                </>
              )}
            </button>
          )}
          
          {hasAudio && !audioCall.loading && (
            <button
              onClick={() => generateAudio(true)}
              disabled={audioCall.loading}
              className="px-6 py-2 border border-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
            >
              Regenerate Audio
            </button>
          )}
        </div>
      )}
      
      {/* Audio Generation Dialog */}
      <GeneratingDialog isOpen={audioCall.loading} type="audio" />
    </div>
  );
});

export default AudioPlayer;