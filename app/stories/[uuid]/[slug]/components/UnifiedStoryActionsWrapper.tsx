'use client';

import React, { useState } from 'react';
import { usePremiumFeatures } from '../../../../hooks/usePremiumFeatures';

import ShareStoryModal from '../../../../components/ShareStoryModal';
import UnifiedStoryActions from './StoryActions';
import { InlineLoadingSpinner } from '../../../../components/ui/LoadingSpinner';

interface StoryData {
  id: number;
  title: string;
  content: string;
  themes: string[];
  ageRange: string;
  createdAt: string;
  imageUrl?: string;
}

interface UnifiedStoryActionsWrapperProps {
  story: StoryData;
  storyId: string;
  storyTitle: string;
  hasExistingAudio: boolean;
  isOwner: boolean;
  isPublic: boolean;
  shareToken?: string;
}

export default function UnifiedStoryActionsWrapper({ 
  story,
  storyId,
  storyTitle,
  hasExistingAudio,
  isOwner,
  isPublic,
  shareToken
}: UnifiedStoryActionsWrapperProps) {
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [highlightingEnabled, setHighlightingEnabled] = useState(true);
  const { isLoading } = usePremiumFeatures();

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const toggleHighlighting = () => {
    setHighlightingEnabled(!highlightingEnabled);
  };

  const handleShare = () => {
    if (isOwner) {
      setIsShareModalOpen(true);
    }
    // For non-owners, share functionality is disabled
  };

  // Show loading spinner while premium features are loading
  if (isLoading) {
    return <InlineLoadingSpinner message="Loading story actions..." />;
  }

  return (
    <>
      {/* Unified Actions and Audio Controls with Progress Bar */}
      <div className="mb-4">
        <UnifiedStoryActions
          story={story}
          storyId={storyId}
          hasExistingAudio={hasExistingAudio}
          highlightingEnabled={highlightingEnabled}
          onShare={handleShare}
          onToggleHighlighting={toggleHighlighting}
          onError={handleError}
          error={error}
          shareToken={shareToken}
        />
      </div>

      {/* Note: Story content with highlighting is now included in UnifiedStoryActions */}

      {/* Share Modal - only for owners */}
      {isOwner && (
        <ShareStoryModal
          isOpen={isShareModalOpen}
          onClose={() => setIsShareModalOpen(false)}
          storyId={storyId}
          storyTitle={storyTitle}
          isPublic={isPublic}
        />
      )}
    </>
  );
} 